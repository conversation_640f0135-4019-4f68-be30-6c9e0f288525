import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView,
  ActivityIndicator,
  Alert,
  Modal,
  Dimensions,
} from "react-native";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { AppImages } from "../utils/AppImages";
import { TextInput } from "react-native-gesture-handler";
import PersonalDetailsPopup from "../components/sections/checkout/PersonalDetailsPopup";

import EmptyCart from "../components/Empty/EmptyCart";
import {
  GetCheckoutApi,
  getSuggestions,
  updateCheckoutItemQuantity,
  removeCheckoutItem
} from "../api/Checkout";
import SuggestedItemSlider from "../components/sections/checkout/SuggestedItemSlider";
import { useDispatch, useSelector } from "react-redux";
import { clearSelectedCoupon } from "../redux/slices/couponSlice";
import {
  setCheckoutData,
  updateItemQuantity,
  removeCartItem,
  setCartLoading,
  setUpdatingQuantity
} from "../redux/slices/cartSlice";
import CartDebugger from "../components/debug/CartDebugger";

const { width } = Dimensions.get("window");

const allergens = [
  { id: 1, name: "Apple", icon: "🍎" },
  { id: 2, name: "Beef", icon: "🥩" },
  { id: 3, name: "Banana", icon: "🍌" },
  { id: 4, name: "Crab", icon: "🦀" },
  { id: 5, name: "Eggs", icon: "🥚" },
  { id: 6, name: "Fish", icon: "🐟" },
  { id: 7, name: "Milk", icon: "🥛" },
  { id: 8, name: "Mushroom", icon: "🍄" },
  { id: 9, name: "Shrimp", icon: "🦐" },
  { id: 10, name: "Pork", icon: "🐷" },
  { id: 11, name: "Mushroom", icon: "🍄" },
  { id: 12, name: "Walnut", icon: "🌰" },
];

const Card = ({ item, vendor, checkoutId, onQuantityUpdate, onRemoveItem }) => {
  const [quantity, setQuantity] = useState(item.quantity);
  const [isUpdating, setIsUpdating] = useState(false);

  // Sync local quantity state with prop changes
  useEffect(() => {
    setQuantity(item.quantity);
  }, [item.quantity]);

  const handleIncrease = async () => {
    if (isUpdating || !checkoutId) return;

    const newQuantity = quantity + 1;
    const originalQuantity = quantity;
    setIsUpdating(true);

    // Optimistically update the UI
    setQuantity(newQuantity);

    try {
      console.log("📈 Increasing quantity for item:", item.id, "from", originalQuantity, "to", newQuantity);
      // Call the parent handler first to update the state
      await onQuantityUpdate(item.id, newQuantity);
      console.log("✅ Quantity increase successful");
    } catch (error) {
      console.error("❌ Error increasing quantity:", error);
      // Revert the optimistic update
      setQuantity(originalQuantity);
      Alert.alert("Error", "Failed to update quantity. Please try again.");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDecrease = async () => {
    if (quantity <= 1 || isUpdating || !checkoutId) return;

    const newQuantity = quantity - 1;
    const originalQuantity = quantity;
    setIsUpdating(true);

    // Optimistically update the UI
    setQuantity(newQuantity);

    try {
      console.log("📉 Decreasing quantity for item:", item.id, "from", originalQuantity, "to", newQuantity);
      // Call the parent handler first to update the state
      await onQuantityUpdate(item.id, newQuantity);
      console.log("✅ Quantity decrease successful");
    } catch (error) {
      console.error("❌ Error decreasing quantity:", error);
      // Revert the optimistic update
      setQuantity(originalQuantity);
      Alert.alert("Error", "Failed to update quantity. Please try again.");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemove = async () => {
    Alert.alert(
      "Remove Item",
      "Are you sure you want to remove this item from cart?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          style: "destructive",
          onPress: async () => {
            setIsUpdating(true);
            try {
              // Call the parent handler to remove the item
              await onRemoveItem(item.id);
            } catch (error) {
              console.error("Error removing item:", error);
              Alert.alert("Error", "Failed to remove item");
            } finally {
              setIsUpdating(false);
            }
          },
        },
      ]
    );
  };

  // Get vendor name from item description or use default
  const getVendorName = () => {    
      return vendor?.vendor_name || "Restaurant";
  };

  return (
    <View style={styles.card}>
      <Image source={{ uri: item.image_url }} style={styles.image} />
      <View style={styles.infoContainer}>
        <View style={styles.textContainer}>
          <Text style={styles.title}>{getVendorName()}</Text>
          <Text style={styles.subtitle}>{item.item_type || "Surprise Bag"}</Text>
          <Text style={styles.price}>₹{item.actual_price}</Text>
        </View>
        <View style={styles.quantityContainer}>
          <TouchableOpacity
            onPress={handleDecrease}
            style={[styles.quantityButton, isUpdating && styles.disabledButton]}
            disabled={isUpdating || quantity <= 1}
          >
            {isUpdating ? (
              <ActivityIndicator size="small" color="#5F22D9" />
            ) : (
              <Text style={styles.quantityButtonText}>-</Text>
            )}
          </TouchableOpacity>
          <Text style={styles.quantity}>
            {quantity.toString().padStart(2, "0")}
          </Text>
          <TouchableOpacity
            onPress={handleIncrease}
            style={[styles.IncreaseQuantityButton, isUpdating && styles.disabledButton]}
            disabled={isUpdating}
          >
            {isUpdating ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.IncreaseQuantityButtonText}>+</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={handleRemove}
        disabled={isUpdating}
      >
        <Text style={styles.removeButtonText}>×</Text>
      </TouchableOpacity>
    </View>
  );
};

const Cart = ({ navigation, route }) => {
  const vendor = useSelector((state) => state.cart.selectedVendor);
  const selectedCoupon = useSelector((state) => state.coupon.selectedCoupon);
  const cartItems = useSelector((state) => state.cart.cartItems);
  const paymentBreakdown = useSelector((state) => state.cart.paymentBreakdown);
  const checkoutData = useSelector((state) => state.cart.checkoutData);
  const isLoading = useSelector((state) => state.cart.isLoading);
  const dispatch = useDispatch();

  const [showEmptyCart, setShowEmptyCart] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [isUpdatingQuantity, setIsUpdatingQuantity] = useState(false);
  const [hasHadItems, setHasHadItems] = useState(false);
  const [allergensModalVisible, setAllergensModalVisible] = useState(false);
  const [selectedAllergens, setSelectedAllergens] = useState([]);

  const fetchSuggestions = async (vendor_id, item_ids) => {
    try {
      console.log("🔍 Fetching suggestions for vendor:", vendor_id, "items:", item_ids);
      const response = await getSuggestions(vendor_id, item_ids);
      console.log("✅ Suggestions response:", response);
      setSuggestions(response || []);
    } catch (error) {
      console.error("❌ Error fetching suggestions:", error);
      setSuggestions([]);
    }
  };

  const GetCheckoutData = async (showLoader = true) => {
    try {
      if (showLoader) {
        dispatch(setCartLoading(true));
      }

      console.log("🔄 Fetching checkout data...");
      const response = await GetCheckoutApi();
      console.log("📋 Get Checkout API response:", response);

      // Check if response is valid
      if (!response || !Array.isArray(response)) {
        console.warn("⚠️ Invalid response format:", response);
        // Only set empty cart if we don't have existing items and not updating
        if (cartItems.length === 0 && !isUpdatingQuantity) {
          setShowEmptyCart(true);
        }
        return;
      }

      // Check if we have any checkouts
      if (response.length === 0) {
        console.log("ℹ️ No active checkouts found");
        // Set empty cart state
        setShowEmptyCart(true);
        setHasHadItems(false);
        dispatch(setCheckoutData(null));
        return;
      }

      const checkout = response[0];
      console.log("📦 Processing checkout:", checkout);

      // Check if checkout has items
      if (!checkout?.checkout_items || checkout.checkout_items.length === 0) {
        console.log("ℹ️ Checkout has no items");
        // Set empty cart state
        setShowEmptyCart(true);
        setHasHadItems(false);
        dispatch(setCheckoutData(null));
        return;
      }

      // Valid checkout with items
      console.log("✅ Valid checkout with", checkout.checkout_items.length, "items");
      dispatch(setCheckoutData(checkout));
      setShowEmptyCart(false);

      // Fetch suggestions for all items
      if (checkout.checkout_items.length > 0) {
        const itemIds = checkout.checkout_items.map(item => item.id);
        fetchSuggestions(checkout.vendor_id, itemIds);
      }
    } catch (error) {
      console.error("❌ Error fetching checkout data:", error);
      if (error.response) {
        console.error("📊 Error response status:", error.response.status);
        console.error("📋 Error response data:", error.response.data);
      }
      // Don't immediately show empty cart on error - keep current state
      console.log("🔄 Keeping current cart state due to error");
    } finally {
      if (showLoader) {
        dispatch(setCartLoading(false));
      }
    }
  };

  const handleQuantityUpdate = async (itemId, newQuantity) => {
    console.log("🔄 Handling quantity update in Cart:", { itemId, newQuantity });

    if (!checkoutData || !checkoutData.checkout_id) {
      console.error("❌ No checkout data available");
      Alert.alert("Error", "No active checkout found. Please refresh the page.");
      return;
    }

    // Prevent showing empty cart during updates
    setIsUpdatingQuantity(true);
    dispatch(setUpdatingQuantity(true));

    // Store the original state for potential rollback
    const originalCheckoutData = { ...checkoutData };

    // Optimistically update the Redux state
    dispatch(updateItemQuantity({ itemId, quantity: newQuantity }));

    try {
      // Call the API with current checkout data to avoid fetching
      console.log("📤 Calling updateCheckoutItemQuantity with current data");
      console.log("📋 Current checkout data:", JSON.stringify(checkoutData, null, 2));
      console.log("📋 Updating item ID:", itemId, "to quantity:", newQuantity);

      const response = await updateCheckoutItemQuantity(
        checkoutData.checkout_id,
        itemId,
        newQuantity,
        checkoutData // Pass current data to avoid fetching
      );

      console.log("✅ API update successful:", response);

      // Update Redux with the response data
      if (response && response.checkout_items) {
        dispatch(setCheckoutData(response));
      }

      console.log("✅ Cart updated successfully");
    } catch (error) {
      console.error("❌ Error updating quantity:", error);

      // Rollback to original state
      console.log("🔄 Rolling back to original state");
      dispatch(setCheckoutData(originalCheckoutData));

      // Show user-friendly error
      Alert.alert(
        "Update Failed",
        "Failed to update quantity. Please check your connection and try again.",
        [{ text: "OK" }]
      );
    } finally {
      // Always reset the updating flag
      setIsUpdatingQuantity(false);
      dispatch(setUpdatingQuantity(false));
    }
  };

  const handleRemoveItem = async (itemId) => {
    console.log("🗑️ Handling item removal in Cart:", itemId);

    if (!checkoutData || !checkoutData.checkout_id) {
      console.error("❌ No checkout data available");
      Alert.alert("Error", "No active checkout found. Please refresh the page.");
      return;
    }

    // Store the original state for potential rollback
    const originalCheckoutData = { ...checkoutData };

    // Optimistically remove from Redux state
    dispatch(removeCartItem(itemId));

    try {
      // Call the API with current checkout data
      console.log("📤 Calling removeCheckoutItem with current data");
      const response = await removeCheckoutItem(
        checkoutData.checkout_id,
        itemId,
        checkoutData // Pass current data to avoid fetching
      );

      console.log("✅ API removal successful:", response);

      // Update Redux with the response data
      if (response && response.checkout_items) {
        dispatch(setCheckoutData(response));
      } else if (response && response.message && response.message.includes("empty checkout items")) {
        // If checkout was cancelled due to empty items, clear the cart
        console.log("📭 Checkout cancelled - no items left");
        dispatch(setCheckoutData(null));
        setShowEmptyCart(true);
        setHasHadItems(false);
      }

      console.log("✅ Item removed successfully");
    } catch (error) {
      console.error("❌ Error removing item:", error);

      // Rollback to original state
      console.log("🔄 Rolling back to original state");
      dispatch(setCheckoutData(originalCheckoutData));

      // Show user-friendly error
      Alert.alert(
        "Remove Failed",
        "Failed to remove item. Please check your connection and try again.",
        [{ text: "OK" }]
      );
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await GetCheckoutData(false);
    setRefreshing(false);
  };

  const toggleAllergen = (allergenId) => {
    setSelectedAllergens((prev) =>
      prev.includes(allergenId)
        ? prev.filter((id) => id !== allergenId)
        : [...prev, allergenId]
    );
  };

  const handleAllergensApply = () => {
    // Handle apply logic here
    setAllergensModalVisible(false);
  };

  const handleAllergensClose = () => {
    setAllergensModalVisible(false);
  };

  const renderAllergenItem = (allergen) => {
    const isSelected = selectedAllergens.includes(allergen.id);
    const isEggs = allergen.name === "Eggs";
    const isMushroom = allergen.name === "Mushroom";

    return (
      <TouchableOpacity
        key={allergen.id}
        style={[
          styles.allergenItem,
          (isEggs || isMushroom) && isSelected && styles.selectedAllergenItem,
        ]}
        onPress={() => toggleAllergen(allergen.id)}
      >
        <Text style={styles.allergenIcon}>{allergen.icon}</Text>
        <Text style={styles.allergenName}>{allergen.name}</Text>
      </TouchableOpacity>
    );
  };

  useEffect(() => {
    GetCheckoutData();

    return () => {
      dispatch(clearSelectedCoupon());
    };
  }, []);

  // Listen for navigation focus to refresh data
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      GetCheckoutData(false);
    });

    return unsubscribe;
  }, [navigation]);

  // Track if we've ever had items to prevent showing empty cart during updates
  useEffect(() => {
    if (cartItems.length > 0) {
      setHasHadItems(true);
      setShowEmptyCart(false);
    }
  }, [cartItems]);

  const [firstModalVisible, setFirstModalVisible] = useState(false);

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text style={styles.headingtext}>Cart</Text>
        </View>
      </View>
      {showEmptyCart ? (
        <EmptyCart />
      ) : cartItems.length === 0 && isUpdatingQuantity ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#5F22D9" />
          <Text style={styles.loadingText}>Loading cart...</Text>
        </View>
      ) : (
        <ScrollView>
          {/* Debug info - remove in production */}
          {/* <CartDebugger visible={__DEV__} /> */}

          {/* Show loading indicator when updating quantities */}
          {isUpdatingQuantity && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#5F22D9" />
              <Text style={styles.loadingText}>Updating cart...</Text>
            </View>
          )}

          <View style={styles.flatListConatiner}>
            <FlatList
              data={cartItems}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <Card
                  item={item}
                  vendor={vendor}
                  checkoutId={checkoutData?.checkout_id}
                  onQuantityUpdate={handleQuantityUpdate}
                  onRemoveItem={handleRemoveItem}
                />
              )}
              refreshing={refreshing}
              onRefresh={onRefresh}
            />
          </View>

          <View style={styles.secondSection}>
            <View style={styles.PromoCodeConatiner}>
              <View style={styles.container1}>
                <TextInput
                  value={selectedCoupon?.code || ""}
                  placeholder={selectedCoupon?.code ? "" : "Promo code"}
                  editable={true}
                  style={styles.textInput}
                  placeholderTextColor="#B0B0B0"
                />
                <View style={styles.promoInnerContainer}>
                  <TouchableOpacity
                    onPress={() => navigation.navigate("Coupon")}
                  >
                    <Image source={AppImages.PROMO} style={styles.promoImage} />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.copyButton}>
                    <Text style={styles.copyText}>Apply</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            {suggestions && suggestions.length > 0 && (
              <View>
                <Text
                  style={{
                    fontFamily: "Poppins_500Medium",
                    fontSize: 16,
                    marginTop: 14,
                  }}
                >
                  Complete your purchase with
                </Text>
                <SuggestedItemSlider
                  data={suggestions}
                  onRefreshCart={() => GetCheckoutData(false)}
                />
              </View>
            )}

            {/* Allergens & Dislikes Section */}
            <View style={styles.allergensInputContainer}>
              <View style={styles.allergensInputContent}>
                <View style={styles.allergensIcon}>
                  <Image source={AppImages.DISLIKE} style={styles.dislikeIcon} />
                </View>
                <Text style={styles.allergensInputText}>Allergens & Dislikes</Text>
              </View>
              <TouchableOpacity
                style={styles.selectInputButton}
                onPress={() => setAllergensModalVisible(true)}
              >
                <Text style={styles.selectInputButtonText}>Select</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.billCard}>
              <Text style={styles.header}>Total Bill</Text>

              <View style={styles.row}>
                <Text style={styles.label}>
                  Quantity ({cartItems.reduce((acc, item) => acc + item.quantity, 0)} items)
                </Text>
                <Text style={styles.boldText}>
                  ₹{paymentBreakdown?.subtotal?.toFixed(0) || ""}
                </Text>
              </View>

              <View style={styles.row}>
                <Text style={styles.label}>Voucher</Text>
                <Text style={styles.purpleText}>
                  -₹{paymentBreakdown?.coupon_discount?.toFixed(0) || ""}
                </Text>
              </View>
            </View>
            <PersonalDetailsPopup
              firstModalVisible={firstModalVisible}
              setFirstModalVisible={setFirstModalVisible}
            />
          </View>
        </ScrollView>
      )}

      {/* Bottom Total and Pay Section - Fixed at bottom */}
      {!showEmptyCart && cartItems.length > 0 && (
        <View style={styles.bottomTotalSection}>
          <View style={styles.totalLeft}>
            <Text style={styles.totalLabel}>
              Total: ₹{paymentBreakdown?.final_amount?.toFixed(0) || "37,50"}
            </Text>
            <Text style={styles.itemsCount}>
              {cartItems.reduce((acc, item) => acc + item.quantity, 0)} items
            </Text>
          </View>
          <TouchableOpacity style={styles.payButton}>
            <Text style={styles.payText}>Pay</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Allergens Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={allergensModalVisible}
        onRequestClose={handleAllergensClose}
      >
        <View style={styles.allergensOverlay}>
          <View style={styles.allergensModalContainer}>
            <TouchableOpacity style={styles.allergensCloseButton} onPress={handleAllergensClose}>
              <Text style={styles.allergensCloseButtonText}>×</Text>
            </TouchableOpacity>

            <Text style={styles.allergensTitle}>
              Let us know what are your{"\n"}allergens & dislikes
            </Text>

            <View style={styles.allergensGrid}>
              {allergens.map((allergen) => renderAllergenItem(allergen))}
            </View>

            <TouchableOpacity style={styles.allergensApplyButton} onPress={handleAllergensApply}>
              <Text style={styles.allergensApplyButtonText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    position: "relative",
  },
  conatinerRoww: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  flatListConatiner: {
    backgroundColor: "#FFF",
    padding: 20,
  },
  secondSection: {
    paddingLeft: 20,
    paddingRight: 20,
    backgroundColor: "#FFFFFF",
  },
  PromoCodeConatiner: {
    backgroundColor: "#FBFBFBBA",
    padding: 16,
    paddingLeft: 20,
    borderTopLeftRadius: 11,
    borderTopRightRadius: 11,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    paddingRight: 20,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 6,
  },
  promoInnerConatiner: {
    flexDirection: "row",
    gap: 6,
    alignItems: "center",
  },
  totalPriceInnerConatinner: {
    flexDirection: "row",
    gap: 1,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  headingtext: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 18,
    color: "#5F22D9",
    marginRight: 35,
  },
  card: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FBFBFBF0",
    marginVertical: 8,
    padding: 16,
    borderTopLeftRadius: 11,
    borderTopRightRadius: 11,
    borderBottomLeftRadius: 11,
    borderBottomRightRadius: 11,
    opacity: 0.87,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 6,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 40,
    resizeMode: "cover",
    overflow: "hidden",
  },
  infoContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 5,
    paddingRight: 32,
    marginLeft: 10,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
    color: "#000000",
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#8C8A9D",
    marginTop: -8,
    marginBottom: 5,
    textTransform: "lowercase",
  },
  price: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#5F22D9",
    marginTop: -8,
  },
  quantityContainer: {
    position: "absolute",
    right: 0,
    flexDirection: "row",
    alignItems: "center",
    marginTop: 34,
  },
  quantityButton: {
    width: 21,
    height: 21,
    borderRadius: 16,
    borderColor: "#5F22D9",
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FFFFFF",
  },
  IncreaseQuantityButton: {
    width: 21,
    height: 21,
    borderRadius: 16,
    borderColor: "#5F22D9",
    backgroundColor: "#5F22D9",
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#AB4CFE",
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },
  quantityButtonText: {
    fontSize: 16,
    color: "#5F22D9",
    lineHeight: 15,
    textAlign: "center",
  },

  IncreaseQuantityButtonText: {
    fontSize: 16,
    color: "#FFF",
    lineHeight: 15,
    textAlign: "center",
  },

  quantity: {
    fontFamily: "Poppins_600SemiBold",
    marginHorizontal: 12,
    fontSize: 16,
    color: "#000",
    minWidth: 24,
    textAlign: "center",
  },
  removeButton: {
    position: "absolute",
    top: 12,
    right: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  removeButtonText: {
    fontSize: 16,
    color: "#5F22D9",
    fontWeight: "bold",
    lineHeight: 16,
  },
  container1: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    borderRadius: 28,
    paddingHorizontal: 10,
    paddingVertical: 7,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    width: "100%",
    alignSelf: "center",
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: "#000",
    marginRight: 10,
    height: "46px",
  },
  promoInnerContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    maxWidth: "40%",
    flexShrink: 1,
  },
  promoImage: {
    width: 25,
    height: 25,
    resizeMode: "contain",
  },
  copyButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 8,
    paddingHorizontal: 18,
    borderRadius: 20,
  },
  copyText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  billCard: {
    backgroundColor: "#FBFBFBF0",
    marginVertical: 8,
    padding: 16,
    borderTopLeftRadius: 11,
    borderTopRightRadius: 11,
    borderBottomLeftRadius: 11,
    borderBottomRightRadius: 11,
    opacity: 0.87,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 6,
  },
  header: {
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    color: "#171725",
    marginBottom: 10,
  },
  rowContainer: {
    marginBottom: 16,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 16,
  },
  totalSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 16,
  },
  totalLeft: {
    flex: 1,
  },
  label: {
    fontSize: 16,
  },
  boldText: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
  },
  purpleText: {
    fontSize: 16,
    color: "#5F22D9",
  },
  total: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  strikeThrough: {
    fontSize: 14,
    color: "#A0A0A0",
    textDecorationLine: "line-through",
    marginRight: 8,
  },
  finalAmount: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#5F22D9",
  },
  paymentContainer: {
    marginTop: 20,
  },
  paymentMethod: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#333",
  },
  cardDetails: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  cardNumber: {
    fontSize: 16,
    color: "#666",
    flex: 1,
    marginLeft: 10,
  },
  changeButton: {
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 17,
    borderColor: "#EDEFFF",
    borderWidth: 2,
  },
  changeText: {
    color: "#5F22D9",
    fontWeight: "bold",
    fontSize: 16,
  },
  totalContainer: {
    marginBottom: 20,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  totalText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  totalTextPrice: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
  },
  itemsText: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#898EBC",
  },
  payButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 16,
    paddingHorizontal: 40,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
    minWidth: 170,
  },
  payText: {
    color: "#FFF",
    fontSize: 18,
    fontWeight: "bold",
  },
  allergensContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
    marginBottom: 16,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  allergensContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  allergensIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#FFFFFF",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  allergensIconText: {
    fontSize: 20,
  },
  dislikeIcon: {
    width: 24,
    height: 24,
    resizeMode: "contain",
  },
  allergensText: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    color: "#8E8E8E",
  },
  selectButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  selectButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  totalLabel: {
    fontSize: 20,
    fontFamily: "Poppins_600SemiBold",
    color: "#171725",
    marginBottom: 4,
  },
  itemsCount: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#898EBC",
  },
  disabledButton: {
    opacity: 0.5,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 10,
    backgroundColor: "#F5F5F5",
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: "#5F22D9",
    fontFamily: "Poppins_400Regular",
  },
  allergensInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    borderRadius: 100,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginTop: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    height: "46px",
  },
  allergensInputContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  allergensInputText: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    color: "#B0B0B0",
    marginLeft: 12,
  },
  selectInputButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 8,
    paddingHorizontal: 18,
    borderRadius: 20,
  },
  selectInputButtonText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  bottomTotalSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: "#FBFBFBC2",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  // Allergens Modal Styles
  allergensOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  allergensModalContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 20,
    padding: 24,
    width: width * 0.9,
    maxWidth: 400,
    position: "relative",
  },
  allergensCloseButton: {
    position: "absolute",
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#F5F5F5",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  allergensCloseButtonText: {
    fontSize: 20,
    color: "#666",
    lineHeight: 20,
  },
  allergensTitle: {
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    color: "#8E8E8E",
    textAlign: "center",
    marginBottom: 32,
    marginTop: 16,
    lineHeight: 24,
  },
  allergensGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: 32,
  },
  allergenItem: {
    width: "30%",
    aspectRatio: 1,
    backgroundColor: "#F8F9FA",
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedAllergenItem: {
    backgroundColor: "#5F22D9",
  },
  allergenIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  allergenName: {
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
    color: "#333",
    textAlign: "center",
  },
  allergensApplyButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 16,
    borderRadius: 25,
    alignItems: "center",
  },
  allergensApplyButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
  },
});

export default Cart;
