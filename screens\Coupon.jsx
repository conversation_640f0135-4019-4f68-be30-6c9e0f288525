import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { AppImages } from "../utils/AppImages";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { CouponsData } from "../utils/Constants";
import { getCoupons } from "../api/Checkout";
import { useDispatch } from "react-redux";
import { setSelectedCoupon } from "../redux/slices/couponSlice";

const Coupon = ({ navigation }) => {
  const [activeCard, setActiveCard] = useState(1);
  const [coupons, setCoupons] = useState([]);
  const dispatch = useDispatch();

  const fetchCoupons = async () => {
    try {
      const response = await getCoupons();

      console.log("all coupons response");
      console.log(response);

      setCoupons(response);
    } catch (error) {
      console.error("Error fetching coupons:", error);
    }
  };

  useEffect(() => {
    fetchCoupons();
  }, []);

  const handleSelectCoupon = (item) => {
    console.log("selected item inside function");
    console.log(item);
    setActiveCard(item.id);
    dispatch(setSelectedCoupon(item));
    navigation.navigate("Cart");
  };

  const renderItem = ({ item }) => {
    const isActive = activeCard === item.id;

    return (
      <View style={styles.card}>
        <View style={styles.discountLabel}>
          <Text style={styles.discountText}>DISCOUNT</Text>
        </View>
        <View style={styles.cardContent}>
          <Text style={styles.offerTitle}>Flat ₹25 off*</Text>
          <Text style={styles.couponCode}>{item.code || "FINFIRST25"}</Text>
          <Text style={styles.offerDescription}>
            Save ₹25 on all transactions.
          </Text>
          <Text style={styles.termsText}>*Terms & conditions</Text>
          <TouchableOpacity
            style={styles.applyCodeButton}
            onPress={() => handleSelectCoupon(item)}
          >
            <Text style={styles.applyCodeText}>Apply Code</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.tagIcon}>
          <Image
            source={AppImages.MASTERCARD_LOGO}
            style={styles.dislikeIcon}
          />
        </View>
      </View>
    );
  };

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text style={styles.headingText}>Coupons</Text>
        </View>
      </View>
      <View style={styles.container1}>
        <Text style={styles.sectionTitle1}>Recommended</Text>
        <FlatList
          data={coupons}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          contentContainerStyle={styles.listContainer}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    padding: 15,
  },
  headingText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
    color: "#5F22D9",
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    position: "relative",
  },
  backButton: {
    padding: 10,
    backgroundColor: "#fff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 5 },
    elevation: 5,
    position: "absolute",
    left: 16,
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  dislikeIcon: {
    width: 32,
    height: 32,
    resizeMode: "contain",
  },
  secondConatiner: {
    marginTop: 10,
    backgroundColor: "#FFFFFF",
    height: "100%",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    gap: 16,
  },
  container1: {
    flex: 1,
    backgroundColor: "#F8F9FA",
    paddingHorizontal: 10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#8E8E8E",
    marginVertical: 12,
  },

  sectionTitle1: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
    color: "#BABABA",
    marginVertical: 12,
    textAlign: "center",
    width: "100%",
  },

  container2: {
    padding: 16,
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 25,
    marginBottom: 16,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    position: "relative",
    overflow: "hidden",
    height: "80%"",
  },
  discountLabel: {
    position: "absolute",
    left: 0,
    top: 0,
    bottom: 0,
    width: 80,
    backgroundColor: "#5F22D9",
    alignItems: "center",
    justifyContent: "center",
    borderTopLeftRadius: 25,
    borderBottomLeftRadius: 25,
  },
  discountText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_600SemiBold",
    transform: [{ rotate: "-90deg" }],
    letterSpacing: 1,
    width: "110%",
  },
  cardContent: {
    marginLeft: 100,
    padding: 20,
    paddingRight: 60,
  },
  offerTitle: {
    fontSize: 14,
    fontFamily: "Poppins_600SemiBold",
    color: "#181E22",
    marginBottom: 4,
  },
  couponCode: {
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    color: "#181E22",
    marginBottom: 8,
  },
  offerDescription: {
    fontSize: 12,
    fontFamily: "Poppins_400Regular",
    color: "#94979F",
    marginBottom: 4,
  },
  termsText: {
    fontSize: 12,
    fontFamily: "Poppins_400Regular",
    color: "#5F22D9",
  },
  tagIcon: {
    position: "absolute",
    top: 16,
    right: 16,
  },
  applyCodeButton: {
    bottom: 16,
    right: 16,
    backgroundColor: "#FFF",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    width: "140%",
  },
  applyCodeText: {
    textAlign: "center",
    textAlignVertical: "center",
    fontSize: 14,
    fontFamily: "Poppins_500Medium",
    color: "#181E22",
  },
});

export default Coupon;
