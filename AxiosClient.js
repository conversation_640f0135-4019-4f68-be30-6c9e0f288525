import axios from "axios";
import { getToken } from "./redux/slices/userSlice";
import { HOSTNAME } from "./config";

const axiosClient = axios.create({
  baseURL: HOSTNAME,
  headers: {
    "Content-Type": "application/json",
  },
});

axiosClient.interceptors.request.use(async (config) => {
  const token = await getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
    console.log("🔐 Token added to request:", token.substring(0, 20) + "...");
    console.log("🔐 I am token:", token);
  } else {
    console.log("⚠️ No token found for request");
  }
  console.log("📤 Request URL:", config.baseURL + config.url);
  console.log("📤 Request method:", config.method);
  console.log("📤 Request data:", JSON.stringify(config.data, null, 2));
  return config;
});

// Add response interceptor for debugging
axiosClient.interceptors.response.use(
  (response) => {
    console.log("📥 Response received:", response.status, response.statusText);
    return response;
  },
  (error) => {
    console.log("❌ Response error:", error.response?.status, error.response?.statusText);
    console.log("❌ Error details:", JSON.stringify(error.response?.data, null, 2));
    return Promise.reject(error);
  }
);

export default axiosClient;
